<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransaksiPenjualanResource\Pages;
use App\Filament\Resources\TransaksiPenjualanResource\RelationManagers;
use App\Models\TransaksiPenjualan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;

class TransaksiPenjualanResource extends Resource
{
    protected static ?string $model = TransaksiPenjualan::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    protected static ?string $navigationGroup = 'Penjualan';

    protected static ?string $navigationLabel = 'Transaksi Penjualan';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Pesanan Penjualan')
                    ->schema([
                        Forms\Components\TextInput::make('kode')
                            ->label('Nomor SO')

                            ->maxLength(50)
                            ->placeholder('Kosongkan untuk default "-" atau isi manual')
                            ->helperText('Jika dikosongkan akan otomatis diisi "-". Untuk referensi gunakan nomor transaksi.'),

                        // media upload sales order
                        SpatieMediaLibraryFileUpload::make('dokumen_so')
                            ->label('Dokumen SO')
                            ->collection('dokumen_so')
                            ->acceptedFileTypes([
                                'application/pdf',
                                'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'image/jpeg',
                                'image/png'
                            ])
                            ->maxSize(10240),

                        Forms\Components\Select::make('tipe')
                            ->label('Tipe Transaksi')
                            ->options([
                                'dagang' => 'Dagang',
                                'jasa' => 'Jasa',
                            ])
                            ->required()
                            ->default('dagang')
                            ->reactive()
                            ->helperText('Pilih tipe transaksi: Dagang untuk penjualan produk, Jasa untuk layanan transportasi'),

                        Forms\Components\DateTimePicker::make('tanggal')
                            ->label('Tanggal Pesanan')
                            ->required()
                            ->default(now()),



                        Forms\Components\Select::make('id_tbbm')
                            ->label('Lokasi TBBM')
                            ->relationship('tbbm', 'nama')
                            ->searchable()
                            ->required()
                            ->preload(),

                        Forms\Components\TextInput::make('nomor_po')
                            ->label('Nomor PO')
                            ->required()
                            ->maxLength(50),

                        // media upload sales order
                        SpatieMediaLibraryFileUpload::make('dokumen_po')
                            ->label('Dokumen PO')
                            ->collection('dokumen_po')
                            ->acceptedFileTypes([
                                'application/pdf',

                            ])
                            ->previewable() // Tampilkan pratinjau link/icon
                            ->downloadable() // Memungkinkan untuk diunduh
                            ->maxSize(10240),
                        Forms\Components\Group::make()
                            ->schema([
                                Forms\Components\Radio::make('sph_input_type')
                                    ->label('Metode Input SPH')
                                    ->options([
                                        'manual' => 'Input Manual',
                                        'select' => 'Pilih dari SPH yang Ada',
                                    ])
                                    ->default(function ($record) {
                                        // Untuk edit: tentukan berdasarkan apakah ada sph_id
                                        if ($record && $record->sph_id) {
                                            return 'select';
                                        }
                                        return 'manual';
                                    })
                                    ->live()
                                    ->dehydrated(false)
                                    ->afterStateUpdated(function (Forms\Set $set, ?string $state) {
                                        // Clear fields when switching input type
                                        if ($state === 'manual') {
                                            $set('sph_id', null);
                                        } else {
                                            $set('nomor_sph', null);
                                        }
                                    })
                                    ->columnSpanFull(),

                                Forms\Components\TextInput::make('nomor_sph')
                                    ->label('Nomor SPH')
                                    ->required(function (Forms\Get $get, $record) {
                                        $inputType = $get('sph_input_type');
                                        // Jika edit dan ada sph_id, tidak required untuk manual input
                                        if ($record && $record->sph_id && $inputType !== 'manual') {
                                            return false;
                                        }
                                        return $inputType === 'manual';
                                    })
                                    ->maxLength(50)
                                    ->visible(function (Forms\Get $get, $record) {
                                        $inputType = $get('sph_input_type');
                                        // Show manual input jika mode manual ATAU jika edit dan tidak ada sph_id
                                        return $inputType === 'manual' || ($record && !$record->sph_id);
                                    })
                                    ->helperText('Masukkan nomor SPH secara manual'),

                                Forms\Components\Select::make('sph_id')
                                    ->label('Pilih SPH')
                                    ->relationship('sph', 'sph_number')
                                    ->searchable()
                                    ->preload()
                                    ->required(function (Forms\Get $get, $record) {
                                        $inputType = $get('sph_input_type');
                                        return $inputType === 'select';
                                    })
                                    ->visible(function (Forms\Get $get, $record) {
                                        $inputType = $get('sph_input_type');
                                        return $inputType === 'select';
                                    })
                                    ->live()
                                    ->afterStateUpdated(function (Forms\Set $set, ?string $state, Forms\Get $get) {
                                        if ($state) {
                                            $sph = \App\Models\Sph::find($state);
                                            if ($sph) {
                                                $set('nomor_sph', $sph->sph_number);
                                                $set('id_pelanggan', $sph->customer_id);

                                                // Set alamat pelanggan jika ada
                                                if ($sph->customer && $sph->customer->alamatPelanggan) {
                                                    $alamat = $sph->customer->alamatPelanggan->where('is_primary', true)->first();
                                                    if ($alamat) {
                                                        $set('id_alamat_pelanggan', $alamat->id);
                                                    }
                                                }
                                            }
                                        } else {
                                            // Jangan clear nomor_sph jika sedang dalam mode select
                                            // Biarkan user tetap bisa lihat nomor SPH yang dipilih
                                        }
                                    })
                                    ->helperText('Pilih SPH yang sudah ada untuk mengisi data otomatis'),
                            ])
                            ->columns(1),

                        // media upload sales order
                        SpatieMediaLibraryFileUpload::make('dokumen_sph')
                            ->label('Dokumen SPH')
                            ->collection('dokumen_sph')
                            ->acceptedFileTypes([
                                'application/pdf',
                                'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'image/jpeg',
                                'image/png'
                            ])
                            ->maxSize(10240),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Informasi Pelanggan')
                    ->schema([
                        // Forms\Components\Select::make('id_subdistrict')
                        //     ->label('Kelurahan')
                        //     ->relationship('subdistrict', 'name')
                        //     ->searchable()
                        //     ->preload(),



                        Forms\Components\Select::make('id_pelanggan')
                            ->label('Pelanggan')
                            ->relationship('pelanggan', 'nama')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->reactive(), // Tambahkan ini untuk membuat field reaktif

                        Forms\Components\Select::make('id_alamat_pelanggan')
                            ->label('Alamat Pelanggan')
                            ->required()
                            ->relationship(
                                name: 'alamatPelanggan',
                                titleAttribute: 'alamat',
                                modifyQueryUsing: fn(Builder $query, $get) =>
                                $query->where('id_pelanggan', $get('id_pelanggan'))
                            )
                            ->getOptionLabelFromRecordUsing(fn($record) => $record->alamat ?? 'Alamat tidak tersedia')
                            ->searchable()
                            ->preload()
                            ->reactive() // Tambahkan ini untuk memastikan pembaruan dinamis
                            ->disabled(fn($get) => !$get('id_pelanggan')),





                        // Forms\Components\TextInput::make('data_dp')
                        //     ->label('Data DP')
                        //     ->numeric()
                        //     ->prefix('IDR')
                        //     ->step(0.01),

                        Forms\Components\TextInput::make('top_pembayaran')
                            ->label('Termin Pembayaran (Hari)')
                            ->numeric()
                            ->suffix('hari'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Detail Item Penjualan')
                    ->description('Tambahkan item-item yang akan dijual dalam transaksi ini')
                    ->schema([
                        Forms\Components\Repeater::make('penjualanDetails')
                            ->label('Item Penjualan')
                            ->relationship()
                            ->required()
                            ->minItems(1)
                            ->schema([
                                Forms\Components\Select::make('id_item')
                                    ->label('Item/Produk')
                                    ->relationship('item', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $item = \App\Models\Item::find($state);
                                            if ($item) {
                                                // Set item info for display
                                                $set('item_info', $item->kode . ' - ' . $item->name);
                                                $set('satuan_info', $item->satuan?->nama ?? '');
                                            }
                                        }
                                    })
                                    ->columnSpan(2),

                                Forms\Components\TextInput::make('volume_item')
                                    ->label('Volume/Kuantitas')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0.01)
                                    ->step(0.01)
                                    ->reactive()
                                    ->suffix(function (callable $get) {
                                        return $get('satuan_info') ?: 'unit';
                                    }),

                                Forms\Components\TextInput::make('harga_jual')
                                    ->label('Harga Jual')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0)
                                    ->prefix('IDR')
                                    ->reactive()
                                    ->columnSpan(2),

                                Forms\Components\Section::make('Lokasi Pengiriman')
                                    ->description('Tentukan lokasi pengiriman untuk item ini')
                                    ->schema([
                                        Forms\Components\Textarea::make('alamat_pengiriman')
                                            ->label('Alamat Pengiriman')
                                            ->placeholder('Masukkan alamat lengkap pengiriman untuk item ini...')
                                            ->rows(2)
                                            ->required()
                                            ->columnSpanFull(),

                                        \Afsakar\LeafletMapPicker\LeafletMapPicker::make('location')
                                            ->label('Lokasi di Peta')
                                            ->defaultLocation([0.5394419, 101.451907]) // LRP default
                                            ->defaultZoom(15)
                                            ->draggable(true)
                                            ->clickable(true)
                                            ->myLocationButtonLabel('Lokasi Saya')
                                            ->tileProvider('openstreetmap')
                                            ->height('300px')
                                            ->columnSpanFull(),

                                        Forms\Components\Textarea::make('keterangan_lokasi')
                                            ->label('Keterangan Lokasi')
                                            ->placeholder('Keterangan tambahan tentang lokasi (opsional)...')
                                            ->rows(2)
                                            ->columnSpanFull(),
                                    ])
                                    ->columnSpanFull()
                                    ->collapsible()
                                    ->collapsed(),

                                Forms\Components\Hidden::make('item_info'),
                                Forms\Components\Hidden::make('satuan_info'),
                            ])
                            ->columns(4)
                            ->defaultItems(1)
                            ->addActionLabel('Tambah Item')
                            ->deleteAction(
                                fn($action) => $action->requiresConfirmation()
                            )
                            ->reorderable(false)
                            ->collapsible()
                            ->itemLabel(function (array $state): ?string {
                                if (!empty($state['item_info'])) {
                                    $volume = floatval($state['volume_item'] ?? 0);
                                    $harga = floatval($state['harga_jual'] ?? 0);
                                    $subtotal = number_format($volume * $harga, 0, ',', '.');
                                    return "{$state['item_info']} - {$volume} x IDR " . number_format($harga, 0, ',', '.') . " = IDR {$subtotal}";
                                }
                                return 'Item Baru';
                            }),
                    ])
                    ->collapsible(),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nomor_transaksi')
                    ->label('Nomor Transaksi')
                    ->searchable(query: function ($query, $search) {
                        return $query->where('id', 'like', "%{$search}%");
                    })
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('kode')
                    ->label('Nomor SO')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('pelanggan.nama')
                    ->label('Pelanggan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('tipe')
                    ->label('Tipe')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'dagang' => 'success',
                        'jasa' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('tanggal')
                    ->label('Tanggal Pesanan')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('nomor_po')
                    ->label('Nomor PO')
                    ->searchable()
                    ->placeholder('T/A'),

                Tables\Columns\TextColumn::make('nomor_sph')
                    ->label('Nomor SPH')
                    ->searchable()
                    ->placeholder('T/A')
                    ->description(fn($record) => $record->sph ? 'Dari SPH: ' . $record->sph->sph_number : null)
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('data_dp')
                    ->label('Data DP')
                    ->money('IDR')
                    ->placeholder('T/A')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('top_pembayaran')
                    ->label('Termin Pembayaran')
                    ->formatStateUsing(fn($state) => $state ? "{$state} hari" : 'Tunai')
                    ->badge()
                    ->color(fn($state) => $state > 30 ? 'warning' : 'success'),

                Tables\Columns\TextColumn::make('tbbm.nama')
                    ->label('Lokasi TBBM')
                    ->placeholder('T/A'),

                Tables\Columns\TextColumn::make('penjualan_details_count')
                    ->label('Jumlah Item')
                    ->counts('penjualanDetails')
                    ->badge()
                    ->color('info'),

                Tables\Columns\IconColumn::make('has_locations')
                    ->label('Lokasi')
                    ->boolean()
                    ->getStateUsing(function ($record) {
                        return $record->penjualanDetails->contains(function ($detail) {
                            return $detail->hasLocation();
                        });
                    })
                    ->trueIcon('heroicon-o-map-pin')
                    ->falseIcon('heroicon-o-x-mark')
                    ->trueColor('success')
                    ->falseColor('gray')
                    ->tooltip(function ($record) {
                        $locationsCount = $record->penjualanDetails->filter(function ($detail) {
                            return $detail->hasLocation();
                        })->count();

                        if ($locationsCount === 0) {
                            return 'Tidak ada lokasi yang ditentukan';
                        }

                        return $locationsCount . ' dari ' . $record->penjualanDetails->count() . ' item memiliki lokasi';
                    }),

                Tables\Columns\IconColumn::make('has_dokumen_sph')
                    ->label('Dokumen SPH')
                    ->boolean()
                    ->getStateUsing(fn($record) => $record->getMedia('dokumen_sph')->count() > 0)
                    ->trueIcon('heroicon-o-document-text')
                    ->falseIcon('heroicon-o-minus')
                    ->trueColor('success')
                    ->falseColor('gray')
                    ->tooltip(fn($record) => $record->getMedia('dokumen_sph')->count() > 0
                        ? $record->getMedia('dokumen_sph')->count() . ' dokumen SPH'
                        : 'Tidak ada dokumen SPH'),

                Tables\Columns\IconColumn::make('has_dokumen_dp')
                    ->label('Dokumen DP')
                    ->boolean()
                    ->getStateUsing(fn($record) => $record->getMedia('dokumen_dp')->count() > 0)
                    ->trueIcon('heroicon-o-document-text')
                    ->falseIcon('heroicon-o-minus')
                    ->trueColor('info')
                    ->falseColor('gray')
                    ->tooltip(fn($record) => $record->getMedia('dokumen_dp')->count() > 0
                        ? $record->getMedia('dokumen_dp')->count() . ' dokumen DP'
                        : 'Tidak ada dokumen DP'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status Approval')
                    ->badge()
                    ->formatStateUsing(fn($state) => match ($state) {
                        'pending_approval' => 'Pending Approval',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                        'needs_revision' => 'Needs Revision',
                        default => ucfirst(str_replace('_', ' ', $state)),
                    })
                    ->color(fn($state) => match ($state) {
                        'pending_approval' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'needs_revision' => 'info',
                        default => 'gray',
                    })
                    ->icon(fn($state) => match ($state) {
                        'pending_approval' => 'heroicon-o-clock',
                        'approved' => 'heroicon-o-check-circle',
                        'rejected' => 'heroicon-o-x-circle',
                        'needs_revision' => 'heroicon-o-arrow-path',
                        default => 'heroicon-o-question-mark-circle',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status Approval')
                    ->options([
                        'pending_approval' => 'Pending Approval',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                        'needs_revision' => 'Needs Revision',
                    ]),

                Tables\Filters\SelectFilter::make('tipe')
                    ->label('Tipe')
                    ->options([
                        'dagang' => 'Dagang',
                        'jasa' => 'Jasa',
                    ]),

                Tables\Filters\SelectFilter::make('id_pelanggan')
                    ->label('Pelanggan')
                    ->relationship('pelanggan', 'nama')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('id_tbbm')
                    ->label('Lokasi TBBM')
                    ->relationship('tbbm', 'nama')
                    ->searchable()
                    ->preload(),

                Tables\Filters\TernaryFilter::make('has_dokumen_sph')
                    ->label('Memiliki Dokumen SPH')
                    ->placeholder('Semua Data')
                    ->trueLabel('Dengan Dokumen SPH')
                    ->falseLabel('Tanpa Dokumen SPH')
                    ->queries(
                        true: fn($query) => $query->whereHas('media', function ($q) {
                            $q->where('collection_name', 'dokumen_sph');
                        }),
                        false: fn($query) => $query->whereDoesntHave('media', function ($q) {
                            $q->where('collection_name', 'dokumen_sph');
                        }),
                    ),

                Tables\Filters\TernaryFilter::make('has_dokumen_dp')
                    ->label('Memiliki Dokumen DP')
                    ->placeholder('Semua Data')
                    ->trueLabel('Dengan Dokumen DP')
                    ->falseLabel('Tanpa Dokumen DP')
                    ->queries(
                        true: fn($query) => $query->whereHas('media', function ($q) {
                            $q->where('collection_name', 'dokumen_dp');
                        }),
                        false: fn($query) => $query->whereDoesntHave('media', function ($q) {
                            $q->where('collection_name', 'dokumen_dp');
                        }),
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),

                // Preview Dokumen Group
                Tables\Actions\ActionGroup::make([
                    // Preview Dokumen SO
                    Tables\Actions\Action::make('preview_dokumen_so')
                        ->label('Preview Dokumen SO')
                        ->icon('heroicon-o-document-text')
                        ->color('info')
                        ->visible(fn(TransaksiPenjualan $record) => $record->getMedia('dokumen_so')->count() > 0)
                        ->modalHeading(fn(TransaksiPenjualan $record) => "Preview Dokumen SO: {$record->kode}")
                        ->modalWidth('6xl')
                        ->slideOver()
                        ->modalSubmitAction(false)
                        ->modalCancelActionLabel('Tutup')
                        ->modalContent(function (TransaksiPenjualan $record) {
                            $media = $record->getFirstMedia('dokumen_so');
                            if (!$media) {
                                return view('filament.preview.no-document');
                            }

                            $fileUrl = $media->getUrl();
                            $fileName = $media->name;
                            $fileExtension = strtolower($media->getExtensionAttribute());

                            return view('filament.preview.document-viewer', [
                                'fileUrl' => $fileUrl,
                                'fileName' => $fileName,
                                'fileExtension' => $fileExtension,
                                'documentType' => 'SO'
                            ]);
                        }),

                    // Preview Dokumen PO
                    Tables\Actions\Action::make('preview_dokumen_po')
                        ->label('Preview Dokumen PO')
                        ->icon('heroicon-o-document-text')
                        ->color('success')
                        ->visible(fn(TransaksiPenjualan $record) => $record->getMedia('dokumen_po')->count() > 0)
                        ->modalHeading(fn(TransaksiPenjualan $record) => "Preview Dokumen PO: {$record->nomor_po}")
                        ->modalWidth('6xl')
                        ->slideOver()
                        ->modalSubmitAction(false)
                        ->modalCancelActionLabel('Tutup')
                        ->modalContent(function (TransaksiPenjualan $record) {
                            $media = $record->getFirstMedia('dokumen_po');
                            if (!$media) {
                                return view('filament.preview.no-document');
                            }

                            $fileUrl = $media->getUrl();
                            $fileName = $media->name;
                            $fileExtension = strtolower($media->getExtensionAttribute());

                            return view('filament.preview.document-viewer', [
                                'fileUrl' => $fileUrl,
                                'fileName' => $fileName,
                                'fileExtension' => $fileExtension,
                                'documentType' => 'PO'
                            ]);
                        }),

                    // Preview Dokumen SPH
                    Tables\Actions\Action::make('preview_dokumen_sph')
                        ->label('Preview Dokumen SPH')
                        ->icon('heroicon-o-document-text')
                        ->color('warning')
                        ->visible(fn(TransaksiPenjualan $record) => $record->getMedia('dokumen_sph')->count() > 0)
                        ->modalHeading(fn(TransaksiPenjualan $record) => "Preview Dokumen SPH: {$record->nomor_sph}")
                        ->modalWidth('6xl')
                        ->slideOver()
                        ->modalSubmitAction(false)
                        ->modalCancelActionLabel('Tutup')
                        ->modalContent(function (TransaksiPenjualan $record) {
                            $media = $record->getFirstMedia('dokumen_sph');
                            if (!$media) {
                                return view('filament.preview.no-document');
                            }

                            $fileUrl = $media->getUrl();
                            $fileName = $media->name;
                            $fileExtension = strtolower($media->getExtensionAttribute());

                            return view('filament.preview.document-viewer', [
                                'fileUrl' => $fileUrl,
                                'fileName' => $fileName,
                                'fileExtension' => $fileExtension,
                                'documentType' => 'SPH'
                            ]);
                        }),
                ])
                    ->label('Preview Dokumen')
                    ->icon('heroicon-o-eye')
                    ->color('gray')
                    ->button(),

                // lihat timeline
                Tables\Actions\Action::make('view_timeline')
                    ->label('Lihat Timeline')
                    ->icon('heroicon-o-clock')
                    ->url(fn(TransaksiPenjualan $record): string => "/admin/sales-order-timeline-detail?record={$record->id}")
                    ->openUrlInNewTab(false),

                // Invoice Actions Group
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('create_invoice')
                        ->label('Buat Invoice')
                        ->icon('heroicon-o-document-plus')
                        ->color('success')
                        ->url(fn($record) => route('filament.admin.resources.invoices.create', [
                            'id_transaksi' => $record->id
                        ]))
                        ->visible(function ($record) {
                            // Only show if no invoice exists yet
                            return !$record->invoices()->exists();
                        })
                        ->tooltip('Buat invoice untuk transaksi ini'),

                    Tables\Actions\Action::make('view_invoices')
                        ->label('Lihat Invoice')
                        ->icon('heroicon-o-eye')
                        ->color('info')
                        ->url(fn($record) => route('filament.admin.resources.invoices.index', [
                            'tableFilters[transaksi_penjualan][value]' => $record->id
                        ]))
                        ->visible(function ($record) {
                            // Only show if invoices exist
                            return $record->invoices()->exists();
                        })
                        ->tooltip('Lihat invoice yang sudah dibuat'),
                ])
                    ->label('Invoice')
                    ->icon('heroicon-o-document-text')
                    ->color('primary')
                    ->button(),

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('tanggal', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\DeliveryOrdersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransaksiPenjualans::route('/'),
            'create' => Pages\CreateTransaksiPenjualan::route('/create'),
            'view' => Pages\ViewTransaksiPenjualan::route('/{record}'),
            'edit' => Pages\EditTransaksiPenjualan::route('/{record}/edit'),
        ];
    }
}
