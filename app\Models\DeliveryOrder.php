<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

use App\Services\MessageService;
use Illuminate\Support\Facades\Log;
use Throwable;

class DeliveryOrder extends Model
{
    protected $table = 'delivery_order';

    protected $fillable = [
        'kode',
        'id_transaksi',
        'id_user',
        'id_kendaraan',
        'tanggal_delivery',
        'no_segel',
        'status_muat',
        'waktu_muat',
        'waktu_selesai_muat',
        'volume_do',
        'volume_details',
        'sisa_volume_do',
        'do_signatory_name',
        'do_print_status',
        'fuel_usage_notes',
        'driver_allowance_amount',
        'allowance_receipt_status',
        'allowance_receipt_time',
        'do_handover_status',
        'do_handover_time',
        'invoice_number',
        'tax_invoice_number',
        'invoice_delivery_status',
        'invoice_archive_status',
        'invoice_confirmation_status',
        'invoice_confirmation_time',
        'payment_status',
        'created_by',
        'status',
        'print_rows_count',
        'print_show_empty_rows',
        'print_custom_notes',
        'print_signature_settings',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'tanggal_delivery' => 'datetime',
        'waktu_muat' => 'datetime',
        'waktu_selesai_muat' => 'datetime',
        'allowance_receipt_time' => 'datetime',
        'do_handover_time' => 'datetime',
        'invoice_confirmation_time' => 'datetime',
        'do_print_status' => 'boolean',
        'allowance_receipt_status' => 'boolean',
        'do_handover_status' => 'boolean',
        'invoice_delivery_status' => 'boolean',
        'invoice_archive_status' => 'boolean',
        'invoice_confirmation_status' => 'boolean',
        'driver_allowance_amount' => 'decimal:2',
        'volume_do' => 'float',
        'status' => 'string',
        'sisa_volume_do' => 'float',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->kode)) {
                $model->kode = $model->generateDoNumber();
            }
        });
    }

    /**
     * Generate automatic DO number
     * Format: DO-MMDDYYYY-0001
     */
    public function generateDoNumber()
    {
        $date = now();
        $prefix = 'DO-' . $date->format('mdY') . '-';

        // Get the last DO number for today
        $lastDo = static::where('kode', 'like', $prefix . '%')
            ->orderBy('kode', 'desc')
            ->first();

        if ($lastDo) {
            // Extract the sequence number and increment
            $lastSequence = intval(substr($lastDo->kode, -4));
            $newSequence = $lastSequence + 1;
        } else {
            $newSequence = 1;
        }

        return $prefix . str_pad($newSequence, 4, '0', STR_PAD_LEFT);
    }

    public function transaksi()
    {
        return $this->belongsTo(TransaksiPenjualan::class, 'id_transaksi');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'id_user');
    }

    /**
     * Get the delivery order details
     */
    public function details()
    {
        return $this->hasMany(DeliveryOrderDetail::class, 'id_delivery_order');
    }

    // on update

    // if status => selesai, update status_muat to selesai

    // Alias for driver (same as user)
    public function driver()
    {
        return $this->user();
    }

    public function kendaraan()
    {
        return $this->belongsTo(Kendaraan::class, 'id_kendaraan');
    }

    /**
     * Get the uang jalan (driver allowance) associated with the delivery order.
     */
    public function uangJalan()
    {
        return $this->hasOne(UangJalan::class, 'id_do');
    }

    /**
     * Get the pengiriman driver (driver delivery) associated with the delivery order.
     */
    public function pengirimanDriver()
    {
        return $this->hasOne(PengirimanDriver::class, 'id_do');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the seals associated with the delivery order.
     */
    public function seals(): HasMany
    {
        return $this->hasMany(DeliveryOrderSeal::class, 'id_delivery_order')->ordered();
    }

    /**
     * Get the invoices through the sales transaction.
     */
    public function invoices()
    {
        return $this->hasManyThrough(
            Invoice::class,
            TransaksiPenjualan::class,
            'id', // Foreign key on transaksi_penjualan table
            'id_transaksi', // Foreign key on invoices table
            'id_transaksi', // Local key on delivery_orders table
            'id' // Local key on transaksi_penjualan table
        );
    }

    /**
     * Get the receipts through the sales transaction.
     */
    public function receipts()
    {
        return $this->hasManyThrough(
            Receipt::class,
            TransaksiPenjualan::class,
            'id', // Foreign key on transaksi_penjualan table
            'id_transaksi', // Foreign key on receipts table
            'id_transaksi', // Local key on delivery_orders table
            'id' // Local key on transaksi_penjualan table
        );
    }

    /**
     * Get the tax invoice associated with the delivery order.
     */
    public function taxInvoice()
    {
        return $this->hasOne(TaxInvoice::class, 'id_do');
    }

    /**
     * Get the total volume from the related sales order (SO).
     */
    public function getTotalSoVolumeAttribute()
    {
        if (!$this->transaksi || !$this->transaksi->penjualanDetails) {
            return 0;
        }

        return $this->transaksi->penjualanDetails->sum('volume_item');
    }

    /**
     * Calculate remaining volume from SO after all deliveries.
     */
    public function calculateRemainingVolume()
    {
        $totalSoVolume = $this->total_so_volume;

        // Get all delivery orders for this SO including current one
        $deliveredVolume = DeliveryOrder::where('id_transaksi', $this->id_transaksi)
            ->sum('volume_do');

        return $totalSoVolume - $deliveredVolume;
    }

    /**
     * Recalculate and update remaining volume for all DOs of this SO.
     */
    public static function recalculateRemainingVolumeForSO($idTransaksi)
    {
        $deliveryOrders = self::where('id_transaksi', $idTransaksi)->get();

        foreach ($deliveryOrders as $do) {
            if ($do->volume_do !== null) {
                $totalSoVolume = $do->total_so_volume;
                $deliveredVolume = self::where('id_transaksi', $idTransaksi)
                    ->where('id', '!=', $do->id)
                    ->sum('volume_do');
                $do->sisa_volume_do = $totalSoVolume - $deliveredVolume - $do->volume_do;
                $do->saveQuietly(); // Save without triggering events
            }
        }
    }

    protected static function booted(): void
    {
        // Auto-calculate remaining volume when saving
        static::saving(function (DeliveryOrder $deliveryOrder) {
            if ($deliveryOrder->id_transaksi && $deliveryOrder->volume_do !== null) {
                $totalSoVolume = $deliveryOrder->total_so_volume;
                $deliveredVolume = DeliveryOrder::where('id_transaksi', $deliveryOrder->id_transaksi)
                    ->where('id', '!=', $deliveryOrder->id ?? 0)
                    ->sum('volume_do');
                $deliveryOrder->sisa_volume_do = $totalSoVolume - $deliveredVolume - $deliveryOrder->volume_do;
            }
        });

        static::created(function (DeliveryOrder $deliveryOrder) {
            if ($deliveryOrder->id_user) {
                try {
                    $messageService = resolve(MessageService::class);

                    $deliveryOrder->loadMissing(['driver', 'kendaraan', 'transaksi.pelanggan', 'transaksi.alamatPelanggan']);

                    $driver      = $deliveryOrder->driver;
                    $vehicle     = $deliveryOrder->kendaraan;
                    $transaction = $deliveryOrder->transaksi;

                    if (!$driver || !$vehicle || !$transaction) {
                        Log::warning("Gagal mengirim notifikasi penugasan DO. Data relasi tidak lengkap.", [
                            'delivery_order_id' => $deliveryOrder->id
                        ]);
                        return;
                    }

                    $redirectUrl = route('filament.admin.resources.delivery-orders.view', ['record' => $deliveryOrder->id]);

                    $transactionData = (object) [
                        'kode'           => $deliveryOrder->kode,
                        'pelanggan_nama' => $transaction->pelanggan?->nama ?? 'N/A',
                        'lokasi_muat'    => $transaction->tbbm?->alamat ?? 'Lokasi tidak ditentukan',
                        'tanggal_jemput' => $deliveryOrder->created_at,
                    ];

                    $messageService->sendDriverAssignmentNotification(
                        $driver,
                        $transactionData,
                        $vehicle,
                        $redirectUrl
                    );
                } catch (Throwable $e) {
                    // 10. Jika terjadi error apapun saat mengirim notifikasi, catat error tersebut.
                    Log::error('Gagal mengirim notifikasi penugasan driver saat pembuatan DO.', [
                        'delivery_order_id' => $deliveryOrder->id,
                        'error_message'     => $e->getMessage(),
                    ]);
                }
            }
        });
    }

    /**
     * Get all seal numbers as a comma-separated string
     */
    public function getSealNumbersAttribute(): string
    {
        return $this->seals->pluck('nomor_segel')->implode(', ');
    }

    /**
     * Get seals grouped by type
     */
    public function getSealsByType(): array
    {
        return $this->seals->groupBy('jenis_segel')->map(function ($seals) {
            return $seals->pluck('nomor_segel')->toArray();
        })->toArray();
    }

    /**
     * Add multiple seals to this delivery order
     */
    public function addSeals(array $seals, ?int $createdBy = null): void
    {
        DeliveryOrderSeal::createMultiple($this->id, $seals, $createdBy);
        $this->load('seals'); // Refresh the relationship
    }
}
